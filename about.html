<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About X-ZoneServers - Enterprise Hosting Solutions | Our Story & Mission</title>
    <meta name="description" content="Learn about X-ZoneServers' mission to provide enterprise-grade hosting solutions. Discover our global infrastructure, expert team, and commitment to 99.9% uptime across 13 worldwide locations.">
    <meta name="keywords" content="about x-zoneservers, hosting company, enterprise hosting, global infrastructure, dedicated servers, VPS hosting, cloud solutions, hosting provider">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>
</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
            
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">
                    <div class="flex items-center justify-center mb-6">
                        <div class="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                            ABOUT US
                        </div>
                        <div class="text-gray-400 text-sm">🚀 Powering Digital Innovation Since 2020</div>
                    </div>

                    <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                        Empowering Your<br>
                        <span class="bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-600 bg-clip-text text-transparent">
                            Digital Success
                        </span>
                    </h1>

                    <p class="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
                        X-ZoneServers is a leading provider of enterprise-grade hosting solutions, delivering unmatched performance, reliability, and support to businesses worldwide through our global infrastructure network.
                    </p>

                    <div class="flex flex-wrap justify-center gap-6 mb-12">
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-indigo-400 rounded-full mr-3 animate-pulse"></div>
                            <span class="text-sm">Founded in 2020</span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                            <span class="text-sm">13 Global Locations</span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-pink-400 rounded-full mr-3"></div>
                            <span class="text-sm">99.9% Uptime SLA</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">5000+</div>
                        <div class="text-indigo-400 font-medium text-sm">Active Servers</div>
                        <div class="text-gray-400 text-xs">Worldwide deployment</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">99.9%</div>
                        <div class="text-purple-400 font-medium text-sm">Uptime SLA</div>
                        <div class="text-gray-400 text-xs">Guaranteed reliability</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">24/7</div>
                        <div class="text-pink-400 font-medium text-sm">Expert Support</div>
                        <div class="text-gray-400 text-xs">Always available</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">13</div>
                        <div class="text-cyan-400 font-medium text-sm">Global Locations</div>
                        <div class="text-gray-400 text-xs">Worldwide presence</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Our Story Section -->
        <section class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <div>
                        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Our Story</h2>
                        <p class="text-xl text-gray-300 mb-6">
                            Founded in 2020, X-ZoneServers emerged from a simple vision: to provide businesses with hosting solutions that never compromise on performance, security, or reliability.
                        </p>
                        <p class="text-gray-400 mb-8">
                            What started as a small team of passionate engineers has grown into a global hosting provider, serving thousands of clients across 13 countries. We've built our reputation on delivering enterprise-grade infrastructure with the personal touch of a dedicated team that truly cares about your success.
                        </p>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-indigo-400 rounded-full mr-4"></div>
                                <span class="text-gray-300">Enterprise-grade infrastructure from day one</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-purple-400 rounded-full mr-4"></div>
                                <span class="text-gray-300">24/7 expert support with real humans</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-pink-400 rounded-full mr-4"></div>
                                <span class="text-gray-300">Continuous innovation and technology upgrades</span>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <!-- Floating Stats Cards -->
                        <div class="space-y-6">
                            <div class="bg-gradient-to-r from-indigo-500/10 to-purple-600/10 backdrop-blur-sm border border-indigo-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-2xl font-bold text-white mb-1">2020</div>
                                        <div class="text-indigo-400 text-sm font-medium">Company Founded</div>
                                    </div>
                                    <div class="w-12 h-12 bg-indigo-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="calendar" class="w-6 h-6 text-indigo-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gradient-to-r from-purple-500/10 to-pink-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300 ml-8">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-2xl font-bold text-white mb-1">Global</div>
                                        <div class="text-purple-400 text-sm font-medium">Infrastructure</div>
                                    </div>
                                    <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="globe" class="w-6 h-6 text-purple-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gradient-to-r from-pink-500/10 to-red-600/10 backdrop-blur-sm border border-pink-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-2xl font-bold text-white mb-1">Expert</div>
                                        <div class="text-pink-400 text-sm font-medium">Team Support</div>
                                    </div>
                                    <div class="w-12 h-12 bg-pink-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="users" class="w-6 h-6 text-pink-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mission & Values Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Our Mission & Values</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        We're committed to empowering businesses with reliable, high-performance hosting solutions that scale with their growth and ambitions.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="group">
                        <div class="bg-gradient-to-br from-indigo-500/10 to-purple-600/10 p-8 rounded-xl border border-indigo-500/20 h-full transition-all duration-300 hover:border-indigo-500/50 hover:shadow-xl hover:shadow-indigo-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="target" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Our Mission</h3>
                            <p class="text-gray-400">To provide enterprise-grade hosting solutions that empower businesses to focus on what they do best, while we handle their infrastructure with unmatched reliability and performance.</p>
                        </div>
                    </div>
                    <div class="group">
                        <div class="bg-gradient-to-br from-purple-500/10 to-pink-600/10 p-8 rounded-xl border border-purple-500/20 h-full transition-all duration-300 hover:border-purple-500/50 hover:shadow-xl hover:shadow-purple-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="heart" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Our Values</h3>
                            <p class="text-gray-400">Reliability, transparency, and customer success drive everything we do. We believe in building long-term partnerships based on trust and exceptional service delivery.</p>
                        </div>
                    </div>
                    <div class="group">
                        <div class="bg-gradient-to-br from-pink-500/10 to-red-600/10 p-8 rounded-xl border border-pink-500/20 h-full transition-all duration-300 hover:border-pink-500/50 hover:shadow-xl hover:shadow-pink-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="trending-up" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Our Vision</h3>
                            <p class="text-gray-400">To be the global leader in enterprise hosting solutions, continuously innovating to meet the evolving needs of businesses in the digital age.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact CTA Section -->
        <section class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl p-12 border border-slate-600/50">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Get Started?</h2>
                    <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                        Join thousands of businesses who trust X-ZoneServers for their hosting needs. Experience the difference of enterprise-grade infrastructure with personal support.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="vps.html" class="btn-primary px-8 py-3 rounded-lg font-semibold inline-flex items-center justify-center">
                            <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                            View Hosting Plans
                        </a>
                        <a href="use-cases.html" class="btn-secondary px-8 py-3 rounded-lg font-semibold inline-flex items-center justify-center">
                            <i data-lucide="layers" class="w-4 h-4 mr-2"></i>
                            Explore Use Cases
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        lucide.createIcons();

        // Animated Lines Background
        const canvas = document.getElementById('lines-canvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            let animationId;

            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            const lines = [];
            const numLines = 8;

            // Create lines with about page theme colors (indigos, purples, pinks)
            for (let i = 0; i < numLines; i++) {
                lines.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    length: Math.random() * 200 + 100,
                    angle: Math.random() * Math.PI * 2,
                    speed: Math.random() * 0.5 + 0.2,
                    opacity: Math.random() * 0.3 + 0.1,
                    color: i % 4 === 0 ? '#6366f1' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#ec4899' : '#ef4444'
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                lines.forEach(line => {
                    // Update position
                    line.x += Math.cos(line.angle) * line.speed;
                    line.y += Math.sin(line.angle) * line.speed;

                    // Wrap around edges
                    if (line.x > canvas.width + line.length) line.x = -line.length;
                    if (line.x < -line.length) line.x = canvas.width + line.length;
                    if (line.y > canvas.height + line.length) line.y = -line.length;
                    if (line.y < -line.length) line.y = canvas.height + line.length;

                    // Draw line with gradient
                    const gradient = ctx.createLinearGradient(
                        line.x, line.y,
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    gradient.addColorStop(0, `${line.color}00`);
                    gradient.addColorStop(0.5, `${line.color}${Math.floor(line.opacity * 255).toString(16).padStart(2, '0')}`);
                    gradient.addColorStop(1, `${line.color}00`);

                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = 2;
                    ctx.lineCap = 'round';

                    ctx.beginPath();
                    ctx.moveTo(line.x, line.y);
                    ctx.lineTo(
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    ctx.stroke();
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();

            // Cleanup on page unload
            window.addEventListener('beforeunload', () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            });
        }
    </script>
</body>
</html>
