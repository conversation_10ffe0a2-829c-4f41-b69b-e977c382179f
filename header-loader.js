// Header loader script with inline header content
function loadHeader() {
    const headerHTML = `
    <!-- Enhanced Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-slate-900/95 to-slate-950/95 backdrop-blur-xl border-b border-slate-800/50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <a href="index.html" class="text-2xl font-bold text-white flex items-center group">

                    <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        X-Zone<span class="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Servers</span>
                    </span>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-1">
                    <a href="vps.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="vps">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                            VPS
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="dedicated.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="dedicated">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                            Dedicated Servers
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="about.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="about">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                            About Us
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-orange-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </nav>

                <!-- CTA Button -->
                <a href="#solutions" class="hidden md:inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105" id="header-cta">
                    <i data-lucide="rocket" class="w-4 h-4 mr-2"></i>
                    Customer Portal
                </a>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden p-2 text-white hover:bg-slate-800/50 rounded-lg transition-colors duration-300">

                </button>
            </div>
        </div>

        <!-- Enhanced Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-gradient-to-br from-slate-950/98 to-slate-900/98 backdrop-blur-xl border-t border-slate-800/50">
            <div class="px-4 py-6 space-y-3">
                <a href="vps.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="vps">
                    <i data-lucide="server" class="w-5 h-5 mr-3"></i>
                    VPS
                </a>
                <a href="dedicated.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="dedicated">
                    <i data-lucide="database" class="w-5 h-5 mr-3"></i>
                    Dedicated Servers
                </a>
                <a href="about.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="about">
                    <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                    About Us
                </a>
                <div class="pt-4 border-t border-slate-800/50">
                    <a href="#solutions" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300" id="mobile-header-cta">
                        <i data-lucide="rocket" class="w-4 h-4 mr-2"></i>
                        Customer Portal
                    </a>
                </div>
            </div>
        </div>
    </header>
    `;

    document.getElementById('header-placeholder').innerHTML = headerHTML;

    // Initialize header functionality after loading
    initializeHeader();
}

function initializeHeader() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Set active navigation based on current page
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

    navLinks.forEach(link => {
        const linkPage = link.getAttribute('data-page');
        if (currentPage === linkPage) {
            // Active page styling
            link.classList.remove('text-gray-300');
            link.classList.add('text-white');
            // Add active background for desktop
            if (link.classList.contains('nav-link')) {
                const bgDiv = link.querySelector('div');
                if (bgDiv) {
                    bgDiv.classList.remove('opacity-0');
                    bgDiv.classList.add('opacity-100');
                }
            }
            // Add active background for mobile
            if (link.classList.contains('mobile-nav-link')) {
                link.classList.add('bg-gradient-to-r', 'from-blue-500/20', 'to-purple-500/20');
            }
        }
    });

    // Update CTA button based on current page
    const headerCta = document.getElementById('header-cta');
    const mobileHeaderCta = document.getElementById('mobile-header-cta');

    if (currentPage === 'vps') {
        if (headerCta) {
            headerCta.innerHTML = '<i data-lucide="zap" class="w-4 h-4 mr-2"></i>Get VPS';
            headerCta.href = '#vps-plans';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.innerHTML = '<i data-lucide="zap" class="w-4 h-4 mr-2"></i>Get VPS';
            mobileHeaderCta.href = '#vps-plans';
        }
    } else if (currentPage === 'dedicated') {
        if (headerCta) {
            headerCta.innerHTML = '<i data-lucide="settings" class="w-4 h-4 mr-2"></i>Configure Server';
            headerCta.href = '#server-configurator';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.innerHTML = '<i data-lucide="settings" class="w-4 h-4 mr-2"></i>Configure Server';
            mobileHeaderCta.href = '#server-configurator';
        }
    } else if (currentPage === 'about') {
        if (headerCta) {
            headerCta.innerHTML = '<i data-lucide="mail" class="w-4 h-4 mr-2"></i>Contact Us';
            headerCta.href = '#contact';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.innerHTML = '<i data-lucide="mail" class="w-4 h-4 mr-2"></i>Contact Us';
            mobileHeaderCta.href = '#contact';
        }
    } else {
        // Default for index page
        if (headerCta) {
            headerCta.innerHTML = '<i data-lucide="rocket" class="w-4 h-4 mr-2"></i>Customer Portal';
            headerCta.href = '#solutions';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.innerHTML = '<i data-lucide="rocket" class="w-4 h-4 mr-2"></i>Customer Portal';
            mobileHeaderCta.href = '#solutions';
        }
    }

    // Re-initialize Lucide icons for dynamically added content
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Load header when DOM is ready
document.addEventListener('DOMContentLoaded', loadHeader);
