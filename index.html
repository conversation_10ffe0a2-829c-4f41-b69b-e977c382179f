<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ZoneServers - High-Performance Dedicated Servers & VPS Hosting | Enterprise Cloud Solutions</title>
    <meta name="description" content="X-ZoneServers provides enterprise-grade dedicated servers and VPS hosting with 10Gbps networks, global locations, and 99.9% uptime SLA. Perfect for web applications, databases, and high-traffic websites.">
    <meta name="keywords" content="dedicated servers, VPS hosting, cloud hosting, enterprise hosting, 10gbps servers, managed hosting, web hosting, database hosting, application hosting, high performance servers">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>

</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Unique Homepage Hero Section -->
        <section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>

            <!-- Radial gradient overlay -->
            <div class="absolute inset-0 bg-gradient-radial from-transparent via-slate-950/30 to-slate-950/80"></div>

            <!-- Floating geometric shapes -->
            <div class="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full blur-2xl animate-pulse"></div>
            <div class="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-pink-500/10 to-red-600/10 rounded-full blur-xl animate-bounce"></div>
            <div class="absolute bottom-32 left-20 w-40 h-40 bg-gradient-to-br from-emerald-500/10 to-teal-600/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-20 right-10 w-28 h-28 bg-gradient-to-br from-yellow-500/10 to-orange-600/10 rounded-full blur-2xl animate-bounce"></div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
                <!-- Central Content Layout -->
                <div class="max-w-6xl mx-auto">
                    <!-- Top Badge Row -->
                    <div class="flex flex-wrap justify-center items-center gap-4 mb-8">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-full text-sm font-bold shadow-lg">
                            <i data-lucide="award" class="w-4 h-4 inline mr-2"></i>
                            ENTERPRISE GRADE
                        </div>
                        <div class="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-6 py-3 rounded-full text-sm font-bold shadow-lg">
                            <i data-lucide="globe" class="w-4 h-4 inline mr-2"></i>
                            13 GLOBAL LOCATIONS
                        </div>
                        <div class="bg-gradient-to-r from-pink-500 to-red-500 text-white px-6 py-3 rounded-full text-sm font-bold shadow-lg">
                            <i data-lucide="zap" class="w-4 h-4 inline mr-2"></i>
                            INSTANT DEPLOY
                        </div>
                    </div>

                    <!-- Main Heading -->
                    <h1 class="text-5xl md:text-7xl lg:text-8xl font-black text-white leading-tight mb-8">
                        <span class="block mb-4">Next-Generation</span>
                        <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent animate-pulse">
                            Cloud Infrastructure
                        </span>
                    </h1>

                    <!-- Subtitle -->
                    <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                        Unleash the power of enterprise-grade hosting with our premium VPS and dedicated servers.
                        <span class="text-blue-400 font-semibold">10Gbps networks</span>,
                        <span class="text-purple-400 font-semibold">99.9% uptime SLA</span>, and
                        <span class="text-pink-400 font-semibold">24/7 expert support</span>
                        across 13 global datacenters.
                    </p>

                    <!-- Interactive Stats Grid -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-4xl mx-auto">
                        <div class="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="text-2xl font-bold text-white mb-1">10Gbps</div>
                            <div class="text-blue-400 text-sm">Max Speed</div>
                        </div>

                        <div class="bg-gradient-to-br from-purple-500/10 to-pink-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="text-2xl font-bold text-white mb-1">99.9%</div>
                            <div class="text-purple-400 text-sm">Uptime SLA</div>
                        </div>

                        <div class="bg-gradient-to-br from-emerald-500/10 to-teal-600/10 backdrop-blur-sm border border-emerald-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="server" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="text-2xl font-bold text-white mb-1">5000+</div>
                            <div class="text-emerald-400 text-sm">Servers</div>
                        </div>

                        <div class="bg-gradient-to-br from-orange-500/10 to-red-600/10 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="headphones" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="text-2xl font-bold text-white mb-1">24/7</div>
                            <div class="text-orange-400 text-sm">Support</div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                        <a href="#solutions" class="group bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-12 py-5 rounded-2xl font-bold text-lg inline-flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105">
                            <i data-lucide="rocket" class="w-6 h-6 mr-3 group-hover:animate-bounce"></i>
                            Explore Solutions
                            <i data-lucide="arrow-right" class="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform"></i>
                        </a>

                        <a href="#features" class="group bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 text-white px-12 py-5 rounded-2xl font-bold text-lg inline-flex items-center justify-center hover:bg-slate-700/50 transition-all duration-300 transform hover:scale-105">
                            <i data-lucide="play-circle" class="w-6 h-6 mr-3"></i>
                            Watch Demo
                        </a>
                    </div>

                    <!-- Scroll Indicator -->
                    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                        <div class="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
                            <div class="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full mt-2 animate-pulse"></div>
                        </div>
                        <p class="text-gray-400 text-xs mt-2">Scroll to explore</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">10Gbps</div>
                        <div class="text-blue-400 font-medium text-sm">Max Bandwidth</div>
                        <div class="text-gray-400 text-xs">Network speed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">512</div>
                        <div class="text-purple-400 font-medium text-sm">Max CPU Cores</div>
                        <div class="text-gray-400 text-xs">Processing power</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">1TB</div>
                        <div class="text-pink-400 font-medium text-sm">Max RAM</div>
                        <div class="text-gray-400 text-xs">Memory capacity</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">13</div>
                        <div class="text-cyan-400 font-medium text-sm">Global Locations</div>
                        <div class="text-gray-400 text-xs">Worldwide reach</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Solutions Overview Section -->
        <section id="solutions" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Choose Your Perfect
                        <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-600 bg-clip-text text-transparent">
                            Hosting Solution
                        </span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        From powerful VPS to enterprise dedicated servers, we have the perfect infrastructure for every business need.
                    </p>
                </div>

                <!-- Two-Column Layout for VPS vs Dedicated -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                    <!-- VPS Section -->
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-600/5 rounded-3xl"></div>
                        <div class="relative p-8 border border-blue-500/20 rounded-3xl backdrop-blur-sm">
                            <div class="flex items-center mb-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4">
                                    <i data-lucide="server" class="w-8 h-8 text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-white">VPS Hosting</h3>
                                    <p class="text-blue-400 font-medium">Virtual Private Servers</p>
                                </div>
                            </div>

                            <p class="text-gray-300 mb-6">
                                Perfect for growing businesses, web applications, and development environments. Get dedicated resources with the flexibility of cloud hosting.
                            </p>

                            <!-- VPS Features Grid -->
                            <div class="grid grid-cols-2 gap-4 mb-8">
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-blue-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">€9.50</div>
                                    <div class="text-blue-400 text-sm">Starting from</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-cyan-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">1-16</div>
                                    <div class="text-cyan-400 text-sm">vCPU Cores</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-purple-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">2-64GB</div>
                                    <div class="text-purple-400 text-sm">RAM Options</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-pink-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">10Gbps</div>
                                    <div class="text-pink-400 text-sm">Network Speed</div>
                                </div>
                            </div>

                            <!-- VPS Use Cases -->
                            <div class="space-y-3 mb-8">
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-blue-400"></i>
                                    <span>Web Applications & APIs</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-cyan-400"></i>
                                    <span>E-commerce Platforms</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-purple-400"></i>
                                    <span>Development & Testing</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-pink-400"></i>
                                    <span>Database Hosting</span>
                                </div>
                            </div>

                            <a href="vps.html" class="btn-primary w-full text-center px-8 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="server" class="w-5 h-5 mr-2"></i>
                                Explore VPS Plans
                            </a>
                        </div>
                    </div>

                    <!-- Dedicated Servers Section -->
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-600/5 rounded-3xl"></div>
                        <div class="relative p-8 border border-purple-500/20 rounded-3xl backdrop-blur-sm">
                            <div class="flex items-center mb-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mr-4">
                                    <i data-lucide="hard-drive" class="w-8 h-8 text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-white">Dedicated Servers</h3>
                                    <p class="text-purple-400 font-medium">Bare Metal Performance</p>
                                </div>
                            </div>

                            <p class="text-gray-300 mb-6">
                                Maximum performance for enterprise applications, high-traffic websites, and resource-intensive workloads. Complete server control and customization.
                            </p>

                            <!-- Dedicated Features Grid -->
                            <div class="grid grid-cols-2 gap-4 mb-8">
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-purple-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">€89</div>
                                    <div class="text-purple-400 text-sm">Starting from</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-pink-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">4-80</div>
                                    <div class="text-pink-400 text-sm">CPU Cores</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-red-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">32-1TB</div>
                                    <div class="text-red-400 text-sm">RAM Options</div>
                                </div>
                                <div class="bg-slate-800/30 p-4 rounded-xl border border-orange-500/10">
                                    <div class="text-2xl font-bold text-white mb-1">100Gbps</div>
                                    <div class="text-orange-400 text-sm">Max Bandwidth</div>
                                </div>
                            </div>

                            <!-- Dedicated Use Cases -->
                            <div class="space-y-3 mb-8">
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-purple-400"></i>
                                    <span>Enterprise Applications</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-pink-400"></i>
                                    <span>High-Traffic Websites</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                                    <span>Game Server Hosting</span>
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-orange-400"></i>
                                    <span>Big Data & Analytics</span>
                                </div>
                            </div>

                            <a href="dedicated.html" class="btn-secondary w-full text-center px-8 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="hard-drive" class="w-5 h-5 mr-2"></i>
                                Explore Dedicated Servers
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Comparison Table -->
                <div class="mt-16 bg-gradient-to-r from-slate-800/30 to-slate-700/30 rounded-2xl p-8 border border-slate-600/50">
                    <h3 class="text-2xl font-bold text-white text-center mb-8">Quick Comparison</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr class="border-b border-slate-600/50">
                                    <th class="pb-4 text-gray-400 font-medium">Feature</th>
                                    <th class="pb-4 text-blue-400 font-medium text-center">VPS Hosting</th>
                                    <th class="pb-4 text-purple-400 font-medium text-center">Dedicated Servers</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-300">
                                <tr class="border-b border-slate-700/30">
                                    <td class="py-4">Starting Price</td>
                                    <td class="py-4 text-center text-blue-400 font-semibold">€9.50/month</td>
                                    <td class="py-4 text-center text-purple-400 font-semibold">€89/month</td>
                                </tr>
                                <tr class="border-b border-slate-700/30">
                                    <td class="py-4">Setup Time</td>
                                    <td class="py-4 text-center">Instant</td>
                                    <td class="py-4 text-center">1 Hour</td>
                                </tr>
                                <tr class="border-b border-slate-700/30">
                                    <td class="py-4">Resource Sharing</td>
                                    <td class="py-4 text-center">Virtualized</td>
                                    <td class="py-4 text-center">Dedicated</td>
                                </tr>
                                <tr class="border-b border-slate-700/30">
                                    <td class="py-4">Root Access</td>
                                    <td class="py-4 text-center text-green-400">✓ Full</td>
                                    <td class="py-4 text-center text-green-400">✓ Complete</td>
                                </tr>
                                <tr>
                                    <td class="py-4">Best For</td>
                                    <td class="py-4 text-center">Web Apps, APIs</td>
                                    <td class="py-4 text-center">Enterprise, High-Traffic</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- Why Choose Us Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Why Choose
                        <span class="bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 bg-clip-text text-transparent">
                            X-ZoneServers?
                        </span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        We deliver enterprise-grade hosting solutions with unmatched performance, reliability, and support.
                    </p>
                </div>

                <!-- Features Grid with Unique Layout -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Feature 1 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-blue-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Lightning Fast Performance</h3>
                            <p class="text-gray-400 mb-6">NVMe SSD storage, 10Gbps network speeds, and optimized configurations ensure your applications run at peak performance.</p>
                            <div class="flex items-center text-blue-400 font-medium">
                                <span>Up to 100Gbps bandwidth</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 2 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-purple-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">99.9% Uptime Guarantee</h3>
                            <p class="text-gray-400 mb-6">Enterprise-grade infrastructure with redundant systems, DDoS protection, and 24/7 monitoring ensures maximum availability.</p>
                            <div class="flex items-center text-purple-400 font-medium">
                                <span>SLA-backed guarantee</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 3 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-emerald-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Global Infrastructure</h3>
                            <p class="text-gray-400 mb-6">13 strategic locations across Europe and North America provide low-latency access to your users worldwide.</p>
                            <div class="flex items-center text-emerald-400 font-medium">
                                <span>13 datacenter locations</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 4 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-orange-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="headphones" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Expert 24/7 Support</h3>
                            <p class="text-gray-400 mb-6">Our experienced technical team provides round-the-clock support via live chat, email, and phone.</p>
                            <div class="flex items-center text-orange-400 font-medium">
                                <span>Real human support</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 5 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-orange-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-yellow-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="settings" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Full Root Access</h3>
                            <p class="text-gray-400 mb-6">Complete control over your server environment with root access, custom configurations, and your choice of operating system.</p>
                            <div class="flex items-center text-yellow-400 font-medium">
                                <span>Complete customization</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Feature 6 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-blue-600/5 rounded-2xl transform group-hover:scale-105 transition-transform duration-300"></div>
                        <div class="relative p-8 border border-cyan-500/20 rounded-2xl backdrop-blur-sm">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                                <i data-lucide="clock" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Instant Deployment</h3>
                            <p class="text-gray-400 mb-6">Get your VPS instantly or dedicated server within 1 hour. Fast provisioning means you can start building immediately.</p>
                            <div class="flex items-center text-cyan-400 font-medium">
                                <span>Quick setup process</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <!-- Global Locations Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Global
                        <span class="bg-gradient-to-r from-emerald-400 via-teal-500 to-cyan-600 bg-clip-text text-transparent">
                            Infrastructure
                        </span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        13 strategic datacenter locations worldwide ensure optimal performance and low latency for your applications.
                    </p>
                </div>

                <!-- Locations Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-12">
                    <div class="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 p-4 rounded-xl text-center border border-blue-500/20 hover:border-blue-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇩🇪</div>
                        <h5 class="text-white text-sm font-semibold">Frankfurt</h5>
                        <p class="text-blue-400 text-xs">Germany</p>
                    </div>
                    <div class="bg-gradient-to-br from-emerald-500/10 to-teal-600/10 p-4 rounded-xl text-center border border-emerald-500/20 hover:border-emerald-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇳🇱</div>
                        <h5 class="text-white text-sm font-semibold">Amsterdam</h5>
                        <p class="text-emerald-400 text-xs">Netherlands</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-500/10 to-pink-600/10 p-4 rounded-xl text-center border border-purple-500/20 hover:border-purple-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇫🇷</div>
                        <h5 class="text-white text-sm font-semibold">Paris</h5>
                        <p class="text-purple-400 text-xs">France</p>
                    </div>
                    <div class="bg-gradient-to-br from-orange-500/10 to-red-600/10 p-4 rounded-xl text-center border border-orange-500/20 hover:border-orange-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇮🇹</div>
                        <h5 class="text-white text-sm font-semibold">Milano</h5>
                        <p class="text-orange-400 text-xs">Italy</p>
                    </div>
                    <div class="bg-gradient-to-br from-yellow-500/10 to-orange-600/10 p-4 rounded-xl text-center border border-yellow-500/20 hover:border-yellow-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇪🇸</div>
                        <h5 class="text-white text-sm font-semibold">Madrid</h5>
                        <p class="text-yellow-400 text-xs">Spain</p>
                    </div>
                    <div class="bg-gradient-to-br from-pink-500/10 to-red-600/10 p-4 rounded-xl text-center border border-pink-500/20 hover:border-pink-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇵🇹</div>
                        <h5 class="text-white text-sm font-semibold">Lisbon</h5>
                        <p class="text-pink-400 text-xs">Portugal</p>
                    </div>
                    <div class="bg-gradient-to-br from-indigo-500/10 to-purple-600/10 p-4 rounded-xl text-center border border-indigo-500/20 hover:border-indigo-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇦🇹</div>
                        <h5 class="text-white text-sm font-semibold">Vienna</h5>
                        <p class="text-indigo-400 text-xs">Austria</p>
                    </div>
                    <div class="bg-gradient-to-br from-teal-500/10 to-cyan-600/10 p-4 rounded-xl text-center border border-teal-500/20 hover:border-teal-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇷🇴</div>
                        <h5 class="text-white text-sm font-semibold">Bucharest</h5>
                        <p class="text-teal-400 text-xs">Romania</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-500/10 to-emerald-600/10 p-4 rounded-xl text-center border border-green-500/20 hover:border-green-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇧🇬</div>
                        <h5 class="text-white text-sm font-semibold">Sofia</h5>
                        <p class="text-green-400 text-xs">Bulgaria</p>
                    </div>
                    <div class="bg-gradient-to-br from-cyan-500/10 to-blue-600/10 p-4 rounded-xl text-center border border-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇵🇱</div>
                        <h5 class="text-white text-sm font-semibold">Warsaw</h5>
                        <p class="text-cyan-400 text-xs">Poland</p>
                    </div>
                    <div class="bg-gradient-to-br from-lime-500/10 to-green-600/10 p-4 rounded-xl text-center border border-lime-500/20 hover:border-lime-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇮🇪</div>
                        <h5 class="text-white text-sm font-semibold">Dublin</h5>
                        <p class="text-lime-400 text-xs">Ireland</p>
                    </div>
                    <div class="bg-gradient-to-br from-red-500/10 to-pink-600/10 p-4 rounded-xl text-center border border-red-500/20 hover:border-red-500/50 transition-all duration-300">
                        <div class="text-3xl mb-2">🇺🇸</div>
                        <h5 class="text-white text-sm font-semibold">Ashburn</h5>
                        <p class="text-red-400 text-xs">United States</p>
                    </div>
                </div>

                <!-- Network Stats -->
                <div class="bg-gradient-to-r from-slate-800/30 to-slate-700/30 rounded-2xl p-8 border border-slate-600/50">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                        <div>
                            <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">13</div>
                            <div class="text-emerald-400 font-medium">Global Locations</div>
                            <div class="text-gray-400 text-sm">Worldwide coverage</div>
                        </div>
                        <div>
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">< 10ms</div>
                            <div class="text-blue-400 font-medium">Average Latency</div>
                            <div class="text-gray-400 text-sm">Lightning fast response</div>
                        </div>
                        <div>
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">99.9%</div>
                            <div class="text-purple-400 font-medium">Network Uptime</div>
                            <div class="text-gray-400 text-sm">Guaranteed reliability</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Premium Features Included</h2>
                    <p class="text-gray-400 mt-2">Everything you need for a powerful hosting experience.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="wifi" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">10Gbps Network Speed</h3>
                        <p class="text-gray-400">Lightning-fast 10Gbps dedicated network ports for seamless streaming and content delivery.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="shield-check" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Free DDoS Protection</h3>
                        <p class="text-gray-400">Advanced DDoS protection included at no extra cost, keeping your streaming services online 24/7.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="map-pin" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Offshore Hosting</h3>
                        <p class="text-gray-400">Strategic offshore locations providing enhanced privacy and content distribution flexibility.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="play-circle" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Streaming Optimized</h3>
                        <p class="text-gray-400">Specially configured servers optimized for streaming, transcoding, and media delivery.</p>
                    </div>
                     <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="hard-drive" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">NVMe Storage</h3>
                        <p class="text-gray-400">Ultra-fast NVMe SSD storage ensuring rapid content loading and smooth streaming performance.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="arrow-right-left" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Unmetered Bandwidth</h3>
                        <p class="text-gray-400">No bandwidth limits or overage charges - stream as much content as you need.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Frequently Asked Questions</h2>
                    <p class="text-gray-400 mt-2">Your questions about our services, answered.</p>
                </div>
                <div class="space-y-4">
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What makes your streaming servers different?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Our streaming servers are specifically optimized for IPTV and media delivery with 10Gbps network speeds, offshore locations for privacy, and specialized configurations for streaming applications. We provide unmetered bandwidth and free DDoS protection as standard.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            Do you support streaming applications?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           Yes! Our servers are specifically configured for streaming with optimized network routing, transcoding capabilities, and content delivery features. We support popular streaming software and provide setup assistance.
                        </div>
                    </details>
                     <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           What offshore locations do you offer?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           We operate in privacy-friendly offshore jurisdictions that provide excellent connectivity and legal protection for content streaming. Our locations are strategically chosen for optimal performance and regulatory advantages.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           How fast can my server be deployed?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           VPS servers are deployed instantly upon payment confirmation. Dedicated streaming servers are typically provisioned within 24-48 hours with full configuration and optimization for your streaming needs.
                        </div>
                    </details>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        // Particle background animation
        const particleCanvas = document.getElementById('particle-canvas');
        if (particleCanvas) {
            const ctx = particleCanvas.getContext('2d');
            const heroSection = document.querySelector('.hero-gradient');
            let particlesArray;

            function setCanvasSize() {
                particleCanvas.width = heroSection.offsetWidth;
                particleCanvas.height = heroSection.offsetHeight;
            }

            class Particle {
                constructor(x, y, directionX, directionY, size, color) {
                    this.x = x;
                    this.y = y;
                    this.directionX = directionX;
                    this.directionY = directionY;
                    this.size = size;
                    this.color = color;
                }
                draw() {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                }
                update() {
                    if (this.x > particleCanvas.width || this.x < 0) {
                        this.directionX = -this.directionX;
                    }
                    if (this.y > particleCanvas.height || this.y < 0) {
                        this.directionY = -this.directionY;
                    }
                    this.x += this.directionX;
                    this.y += this.directionY;
                    this.draw();
                }
            }

            function init() {
                particlesArray = [];
                let numberOfParticles = (particleCanvas.height * particleCanvas.width) / 12000;
                for (let i = 0; i < numberOfParticles; i++) {
                    let size = (Math.random() * 1.5) + 1;
                    let x = (Math.random() * ((particleCanvas.width - size * 2) - (size * 2)) + size * 2);
                    let y = (Math.random() * ((particleCanvas.height - size * 2) - (size * 2)) + size * 2);
                    let directionX = (Math.random() * 0.4) - 0.2;
                    let directionY = (Math.random() * 0.4) - 0.2;
                    let color = 'rgba(56, 189, 248, 0.7)';
                    particlesArray.push(new Particle(x, y, directionX, directionY, size, color));
                }
            }

            function connect() {
                let opacityValue = 1;
                for (let a = 0; a < particlesArray.length; a++) {
                    for (let b = a; b < particlesArray.length; b++) {
                        let distance = ((particlesArray[a].x - particlesArray[b].x) * (particlesArray[a].x - particlesArray[b].x)) +
                            ((particlesArray[a].y - particlesArray[b].y) * (particlesArray[a].y - particlesArray[b].y));
                        
                        let maxDistance = 150;
                        if (distance < maxDistance * maxDistance) {
                            opacityValue = 1 - (distance / (maxDistance * maxDistance));
                            ctx.strokeStyle = `rgba(56, 189, 248, ${opacityValue * 0.8})`;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
                            ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
                            ctx.stroke();
                        }
                    }
                }
            }

            function animate() {
                requestAnimationFrame(animate);
                ctx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
                for (let i = 0; i < particlesArray.length; i++) {
                    particlesArray[i].update();
                }
                connect();
            }

            window.addEventListener('resize', () => {
                setCanvasSize();
                init();
            });

            setCanvasSize();
            init();
            animate();
        }
    </script>

    <script>
        lucide.createIcons();

        // FAQ accordion behavior
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(details => {
            details.addEventListener('toggle', event => {
                const icon = details.querySelector('summary i');
                if (details.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // Location slider functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('#location-slider > div');
        const dots = document.querySelectorAll('.slider-dot');
        const slider = document.getElementById('location-slider');

        function updateSlider() {
            slider.style.transform = `translateX(-${currentSlide * 100}%)`;
            dots.forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.remove('bg-gray-400', 'opacity-50');
                    dot.classList.add('bg-sky-400', 'opacity-100');
                } else {
                    dot.classList.remove('bg-sky-400', 'opacity-100');
                    dot.classList.add('bg-gray-400', 'opacity-50');
                }
            });
        }

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlider();
            });
        });

        // Auto-advance slider
        setInterval(() => {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlider();
        }, 4000);

        // Animated Lines Background
        const canvas = document.getElementById('lines-canvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            let animationId;

            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            const lines = [];
            const numLines = 10;

            // Create lines with homepage theme colors (blues, purples, pinks)
            for (let i = 0; i < numLines; i++) {
                lines.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    length: Math.random() * 200 + 100,
                    angle: Math.random() * Math.PI * 2,
                    speed: Math.random() * 0.5 + 0.2,
                    opacity: Math.random() * 0.3 + 0.1,
                    color: i % 4 === 0 ? '#3b82f6' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#ec4899' : '#06b6d4'
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                lines.forEach(line => {
                    // Update position
                    line.x += Math.cos(line.angle) * line.speed;
                    line.y += Math.sin(line.angle) * line.speed;

                    // Wrap around edges
                    if (line.x > canvas.width + line.length) line.x = -line.length;
                    if (line.x < -line.length) line.x = canvas.width + line.length;
                    if (line.y > canvas.height + line.length) line.y = -line.length;
                    if (line.y < -line.length) line.y = canvas.height + line.length;

                    // Draw line with gradient
                    const gradient = ctx.createLinearGradient(
                        line.x, line.y,
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    gradient.addColorStop(0, `${line.color}00`);
                    gradient.addColorStop(0.5, `${line.color}${Math.floor(line.opacity * 255).toString(16).padStart(2, '0')}`);
                    gradient.addColorStop(1, `${line.color}00`);

                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = 2;
                    ctx.lineCap = 'round';

                    ctx.beginPath();
                    ctx.moveTo(line.x, line.y);
                    ctx.lineTo(
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    ctx.stroke();
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();

            // Cleanup on page unload
            window.addEventListener('beforeunload', () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            });
        }



        // Initialize Lucide icons
        lucide.createIcons();

    </script>
</body>
</html>