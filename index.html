<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ZoneServers - Offshore Dedicated Streaming Servers | 10Gbps Media Hosting</title>
    <meta name="description" content="X-ZoneServers SRL provides fastest offshore dedicated streaming servers with 10Gbps network, free DDoS protection. Perfect for streaming and media delivery.">
    <meta name="keywords" content="offshore dedicated servers, streaming servers, media hosting, 10gbps dedicated servers, offshore hosting, streaming vps, dedicated streaming, ddos protection, media servers">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>

</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section -->
        <section class="pt-32 pb-20 relative overflow-hidden hero-gradient">
            <canvas id="particle-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-extrabold text-white leading-tight mb-4">
                    Fastest Offshore Streaming Servers
                </h1>
                <p class="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto mb-8">
                    Power your streaming services with our dedicated 10Gbps offshore servers. Free DDoS protection, instant deployment, and unmetered bandwidth for seamless media delivery.
                </p>
                <div class="flex justify-center gap-4">
                    <a href="#streaming-vps" class="btn-primary px-8 py-4 rounded-lg font-semibold text-lg">View Streaming Plans</a>
                </div>
            </div>
        </section>

        <!-- VPS Pricing Section -->
        <section id="streaming-vps" class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Offshore Streaming VPS Plans</h2>
                    <p class="text-gray-400 mt-2 max-w-2xl mx-auto">High-performance streaming servers optimized for media delivery and content distribution.</p>
                </div>

                <div class="relative">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 justify-items-center">
                    <!-- VPS Plan 1 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Streaming VPS S1</h3>
                            <p class="text-gray-400 text-sm mb-4">Entry-level streaming server.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">$19.99</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>2 vCPU Cores</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>4 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>100 GB NVMe SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>1Gbps Port</li>
                                <li class="flex items-center"><i data-lucide="shield-check" class="w-4 h-4 mr-2 text-sky-400"></i>Free DDoS Protection</li>
                                <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-sky-400"></i>Est. 1-2Gbps Speed</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Order Now</a>
                    </div>

                    <!-- VPS Plan 2 -->
                    <div class="pricing-card rounded-xl p-6">
                         <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Streaming VPS S2</h3>
                            <p class="text-gray-400 text-sm mb-4">Popular choice for streaming.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">$39.99</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                           <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>4 vCPU Cores</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>8 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>200 GB NVMe SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>2.5Gbps Port</li>
                                <li class="flex items-center"><i data-lucide="shield-check" class="w-4 h-4 mr-2 text-sky-400"></i>Free DDoS Protection</li>
                                <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-sky-400"></i>Est. 2-3Gbps Speed</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Order Now</a>
                    </div>
                    
                    <!-- VPS Plan 3 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">VPS III KVM</h3>
                            <p class="text-gray-400 text-sm mb-4">Dual-core streaming power.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€26.99</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>2 vCPU Cores</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>4 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>80 GB SSD Storage</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>10Gbps Bandwidth</li>
                                <li class="flex items-center"><i data-lucide="arrow-right-left" class="w-4 h-4 mr-2 text-sky-400"></i>Unmetered Traffic</li>
                                <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-sky-400"></i>Est. 3-4Gbps Speed</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Order Now</a>
                    </div>
                    
                    <!-- VPS Plan 4 -->
                    <div class="pricing-card rounded-xl p-6" style="background: linear-gradient(to right, rgba(30, 41, 59, 0.5) 0%, rgba(30, 41, 59, 0.5) 70%, rgba(30, 41, 59, 0) 100%);">
                         <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">VPS IV KVM</h3>
                            <p class="text-gray-400 text-sm mb-4">Quad-core streaming power.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€64.99</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                           <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>4 vCPU Cores</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>8 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>160 GB SSD Storage</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>10Gbps Bandwidth</li>
                                <li class="flex items-center"><i data-lucide="arrow-right-left" class="w-4 h-4 mr-2 text-sky-400"></i>Unmetered Traffic</li>
                                <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-sky-400"></i>Est. 4-5Gbps Speed</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Order Now</a>
                    </div>
                    </div>
                    
                    <!-- VPS View More Indicator positioned next to last card -->
                    <a href="vps.html" 
                    class="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-16 lg:block hidden">
                        <div class="text-center cursor-pointer">
                            <div class="text-gray-400 text-lg mb-1">•••</div>
                            <p class="text-xs text-gray-500">view more</p>
                        </div>
                    </a>
                </div>
            </div>
        </section>

        <!-- Dedicated Servers Section -->
        <section id="dedicated-streaming" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Dedicated Streaming Servers</h2>
                    <p class="text-gray-400 mt-2 max-w-2xl mx-auto">Ultimate performance for large-scale streaming operations with guaranteed bandwidth across 13 global locations.</p>
                </div>

                <div class="relative">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
                    <!-- Dedicated Server 1 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon E5-2690v4</h3>
                            <p class="text-gray-400 text-sm mb-4">High-performance dedicated server.</p>
                             <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€229</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>28 Core / 56 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>128 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>1/5/10Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>
                    
                    <!-- Dedicated Server 2 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon E5-2699v4</h3>
                            <p class="text-gray-400 text-sm mb-4">Premium dedicated streaming.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€299</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>40 Core / 80 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>256 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>1/5/10Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>

                    <!-- Dedicated Server 3 -->
                    <div class="pricing-card rounded-xl p-6" style="background: linear-gradient(to right, rgba(30, 41, 59, 0.5) 0%, rgba(30, 41, 59, 0.5) 70%, rgba(30, 41, 59, 0) 100%);">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon Gold 6148</h3>
                            <p class="text-gray-400 text-sm mb-4">Ultimate streaming performance.</p>
                             <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€1799</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>40 Core / 80 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>256 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>25/50/100Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>
                    </div>
                    
                    <!-- Dedicated View More Indicator positioned next to last card -->
                            <a href="dedicated.html" 
                   class="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-16 lg:block hidden">
                        <div class="text-center">
                            <div class="text-gray-400 text-lg mb-1">•••</div>
                            <p class="text-xs text-gray-500">view more</p>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Locations Section -->
        <section class="py-16 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Global Server Locations</h2>
                    <p class="text-gray-400 mt-2">Premium datacenters across Europe and US</p>
                </div>
                
                <!-- Slider Container -->
                <div class="relative overflow-hidden">
                    <div id="location-slider" class="flex transition-transform duration-500 ease-in-out">
                        <!-- Slide 1 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇷🇴</div>
                                    <h5 class="text-white text-sm font-semibold">Bucharest</h5>
                                    <p class="text-gray-400 text-xs">Voxility IRD</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Milan</h5>
                                    <p class="text-gray-400 text-xs">Irideos, Seeweb</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Rome</h5>
                                    <p class="text-gray-400 text-xs">Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇨🇿</div>
                                    <h5 class="text-white text-sm font-semibold">Prague</h5>
                                    <p class="text-gray-400 text-xs">CE Colo</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 2 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇪🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Madrid</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇫🇷</div>
                                    <h5 class="text-white text-sm font-semibold">Paris</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇫🇷</div>
                                    <h5 class="text-white text-sm font-semibold">Marseille</h5>
                                    <p class="text-gray-400 text-xs">Multiple DCs</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇬🇧</div>
                                    <h5 class="text-white text-sm font-semibold">London</h5>
                                    <p class="text-gray-400 text-xs">Telehouse, Equinix</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 3 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇪</div>
                                    <h5 class="text-white text-sm font-semibold">Dublin</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇸🇪</div>
                                    <h5 class="text-white text-sm font-semibold">Stockholm</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇨🇭</div>
                                    <h5 class="text-white text-sm font-semibold">Zurich</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇵🇱</div>
                                    <h5 class="text-white text-sm font-semibold">Warsaw</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 4 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇧🇬</div>
                                    <h5 class="text-white text-sm font-semibold">Sofia</h5>
                                    <p class="text-gray-400 text-xs">Telepoint Centre</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Ashburn</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Miami</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Dallas</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Dots -->
                    <div class="flex justify-center mt-6 space-x-2">
                        <button class="slider-dot w-3 h-3 rounded-full bg-sky-400 opacity-100"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Premium Features Included</h2>
                    <p class="text-gray-400 mt-2">Everything you need for a powerful hosting experience.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="wifi" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">10Gbps Network Speed</h3>
                        <p class="text-gray-400">Lightning-fast 10Gbps dedicated network ports for seamless streaming and content delivery.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="shield-check" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Free DDoS Protection</h3>
                        <p class="text-gray-400">Advanced DDoS protection included at no extra cost, keeping your streaming services online 24/7.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="map-pin" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Offshore Hosting</h3>
                        <p class="text-gray-400">Strategic offshore locations providing enhanced privacy and content distribution flexibility.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="play-circle" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Streaming Optimized</h3>
                        <p class="text-gray-400">Specially configured servers optimized for streaming, transcoding, and media delivery.</p>
                    </div>
                     <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="hard-drive" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">NVMe Storage</h3>
                        <p class="text-gray-400">Ultra-fast NVMe SSD storage ensuring rapid content loading and smooth streaming performance.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="arrow-right-left" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Unmetered Bandwidth</h3>
                        <p class="text-gray-400">No bandwidth limits or overage charges - stream as much content as you need.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Frequently Asked Questions</h2>
                    <p class="text-gray-400 mt-2">Your questions about our services, answered.</p>
                </div>
                <div class="space-y-4">
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What makes your streaming servers different?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Our streaming servers are specifically optimized for IPTV and media delivery with 10Gbps network speeds, offshore locations for privacy, and specialized configurations for streaming applications. We provide unmetered bandwidth and free DDoS protection as standard.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            Do you support streaming applications?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           Yes! Our servers are specifically configured for streaming with optimized network routing, transcoding capabilities, and content delivery features. We support popular streaming software and provide setup assistance.
                        </div>
                    </details>
                     <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           What offshore locations do you offer?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           We operate in privacy-friendly offshore jurisdictions that provide excellent connectivity and legal protection for content streaming. Our locations are strategically chosen for optimal performance and regulatory advantages.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           How fast can my server be deployed?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           VPS servers are deployed instantly upon payment confirmation. Dedicated streaming servers are typically provisioned within 24-48 hours with full configuration and optimization for your streaming needs.
                        </div>
                    </details>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        // Particle background animation
        const particleCanvas = document.getElementById('particle-canvas');
        if (particleCanvas) {
            const ctx = particleCanvas.getContext('2d');
            const heroSection = document.querySelector('.hero-gradient');
            let particlesArray;

            function setCanvasSize() {
                particleCanvas.width = heroSection.offsetWidth;
                particleCanvas.height = heroSection.offsetHeight;
            }

            class Particle {
                constructor(x, y, directionX, directionY, size, color) {
                    this.x = x;
                    this.y = y;
                    this.directionX = directionX;
                    this.directionY = directionY;
                    this.size = size;
                    this.color = color;
                }
                draw() {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                }
                update() {
                    if (this.x > particleCanvas.width || this.x < 0) {
                        this.directionX = -this.directionX;
                    }
                    if (this.y > particleCanvas.height || this.y < 0) {
                        this.directionY = -this.directionY;
                    }
                    this.x += this.directionX;
                    this.y += this.directionY;
                    this.draw();
                }
            }

            function init() {
                particlesArray = [];
                let numberOfParticles = (particleCanvas.height * particleCanvas.width) / 12000;
                for (let i = 0; i < numberOfParticles; i++) {
                    let size = (Math.random() * 1.5) + 1;
                    let x = (Math.random() * ((particleCanvas.width - size * 2) - (size * 2)) + size * 2);
                    let y = (Math.random() * ((particleCanvas.height - size * 2) - (size * 2)) + size * 2);
                    let directionX = (Math.random() * 0.4) - 0.2;
                    let directionY = (Math.random() * 0.4) - 0.2;
                    let color = 'rgba(56, 189, 248, 0.7)';
                    particlesArray.push(new Particle(x, y, directionX, directionY, size, color));
                }
            }

            function connect() {
                let opacityValue = 1;
                for (let a = 0; a < particlesArray.length; a++) {
                    for (let b = a; b < particlesArray.length; b++) {
                        let distance = ((particlesArray[a].x - particlesArray[b].x) * (particlesArray[a].x - particlesArray[b].x)) +
                            ((particlesArray[a].y - particlesArray[b].y) * (particlesArray[a].y - particlesArray[b].y));
                        
                        let maxDistance = 150;
                        if (distance < maxDistance * maxDistance) {
                            opacityValue = 1 - (distance / (maxDistance * maxDistance));
                            ctx.strokeStyle = `rgba(56, 189, 248, ${opacityValue * 0.8})`;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
                            ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
                            ctx.stroke();
                        }
                    }
                }
            }

            function animate() {
                requestAnimationFrame(animate);
                ctx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
                for (let i = 0; i < particlesArray.length; i++) {
                    particlesArray[i].update();
                }
                connect();
            }

            window.addEventListener('resize', () => {
                setCanvasSize();
                init();
            });

            setCanvasSize();
            init();
            animate();
        }
    </script>

    <script>
        lucide.createIcons();

        // FAQ accordion behavior
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(details => {
            details.addEventListener('toggle', event => {
                const icon = details.querySelector('summary i');
                if (details.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // Location slider functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('#location-slider > div');
        const dots = document.querySelectorAll('.slider-dot');
        const slider = document.getElementById('location-slider');

        function updateSlider() {
            slider.style.transform = `translateX(-${currentSlide * 100}%)`;
            dots.forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.remove('bg-gray-400', 'opacity-50');
                    dot.classList.add('bg-sky-400', 'opacity-100');
                } else {
                    dot.classList.remove('bg-sky-400', 'opacity-100');
                    dot.classList.add('bg-gray-400', 'opacity-50');
                }
            });
        }

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlider();
            });
        });

        // Auto-advance slider
        setInterval(() => {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlider();
        }, 4000);

    </script>
</body>
</html>