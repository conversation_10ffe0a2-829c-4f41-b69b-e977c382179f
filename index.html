<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ZoneServers - High-Performance Dedicated Servers & VPS Hosting | Enterprise Cloud Solutions</title>
    <meta name="description" content="X-ZoneServers provides enterprise-grade dedicated servers and VPS hosting with 10Gbps networks, global locations, and 99.9% uptime SLA. Perfect for web applications, databases, and high-traffic websites.">
    <meta name="keywords" content="dedicated servers, VPS hosting, cloud hosting, enterprise hosting, 10gbps servers, managed hosting, web hosting, database hosting, application hosting, high performance servers">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>

</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <!-- Left Content -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                ENTERPRISE GRADE
                            </div>
                            <div class="text-gray-400 text-sm">🌍 13 Global Locations</div>
                        </div>

                        <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                            High-Performance<br>
                            <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-600 bg-clip-text text-transparent">
                                Hosting Solutions
                            </span>
                        </h1>

                        <p class="text-xl text-gray-300 mb-8">
                            Power your applications with enterprise-grade dedicated servers and VPS hosting. Get guaranteed bandwidth, 99.9% uptime SLA, and premium infrastructure across global datacenters.
                        </p>

                        <div class="flex flex-wrap gap-6 mb-12">
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                                <span class="text-sm">Instant Deployment</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                                <span class="text-sm">99.9% Uptime SLA</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-pink-400 rounded-full mr-3"></div>
                                <span class="text-sm">24/7 Expert Support</span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#hosting-plans" class="btn-primary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="server" class="w-5 h-5 mr-2"></i>
                                View Hosting Plans
                            </a>
                            <a href="#features" class="btn-secondary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                                Learn More
                            </a>
                        </div>
                    </div>

                    <!-- Right Content - Floating Stats -->
                    <div class="relative hidden lg:block">
                        <!-- Floating Performance Cards -->
                        <div class="space-y-6">
                            <!-- Top Card -->
                            <div class="bg-gradient-to-r from-blue-500/10 to-purple-600/10 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">10Gbps</div>
                                        <div class="text-blue-400 text-sm font-medium">Network Speed</div>
                                    </div>
                                    <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="zap" class="w-6 h-6 text-blue-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Middle Card -->
                            <div class="bg-gradient-to-r from-purple-500/10 to-pink-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300 ml-8">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">99.9%</div>
                                        <div class="text-purple-400 text-sm font-medium">Uptime SLA</div>
                                    </div>
                                    <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="shield-check" class="w-6 h-6 text-purple-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Card -->
                            <div class="bg-gradient-to-r from-pink-500/10 to-red-600/10 backdrop-blur-sm border border-pink-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">24/7</div>
                                        <div class="text-pink-400 text-sm font-medium">Expert Support</div>
                                    </div>
                                    <div class="w-12 h-12 bg-pink-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="headphones" class="w-6 h-6 text-pink-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-xl"></div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-pink-400/20 to-red-600/20 rounded-full blur-xl"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">10Gbps</div>
                        <div class="text-blue-400 font-medium text-sm">Max Bandwidth</div>
                        <div class="text-gray-400 text-xs">Network speed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">512</div>
                        <div class="text-purple-400 font-medium text-sm">Max CPU Cores</div>
                        <div class="text-gray-400 text-xs">Processing power</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">1TB</div>
                        <div class="text-pink-400 font-medium text-sm">Max RAM</div>
                        <div class="text-gray-400 text-xs">Memory capacity</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">13</div>
                        <div class="text-cyan-400 font-medium text-sm">Global Locations</div>
                        <div class="text-gray-400 text-xs">Worldwide reach</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VPS Hosting Section -->
        <section id="hosting-plans" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">VPS Hosting Plans</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        High-performance virtual private servers with dedicated resources, perfect for web applications, databases, and high-traffic websites.
                    </p>
                </div>

                <div class="relative">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 justify-items-center">
                    <!-- VPS Plan 1 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-blue-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS Starter</h4>
                                    <p class="text-blue-400 font-medium text-sm">Entry Level</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€9.50</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">2 GB RAM</span>
                                    <span class="text-gray-300">1 vCPU</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">20 GB SSD</span>
                                    <span class="text-blue-400 font-semibold">10Gbps</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300">
                                    Order Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 2 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-emerald-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS Business</h4>
                                    <p class="text-emerald-400 font-medium text-sm">Popular Choice</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€26.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">4 GB RAM</span>
                                    <span class="text-gray-300">2 vCPU</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">80 GB SSD</span>
                                    <span class="text-emerald-400 font-semibold">10Gbps</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-emerald-600 hover:to-teal-700 transition-all duration-300">
                                    Order Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 3 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-purple-500/50 hover:bg-slate-800/50">
                            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-bold">
                                    POPULAR
                                </div>
                            </div>

                            <div class="flex items-center justify-between mb-4 mt-2">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS Pro</h4>
                                    <p class="text-purple-400 font-medium text-sm">High Performance</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€64.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">8 GB RAM</span>
                                    <span class="text-gray-300">4 vCPU</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">160 GB SSD</span>
                                    <span class="text-purple-400 font-semibold">10Gbps</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-700 transition-all duration-300">
                                    Order Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 4 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS Enterprise</h4>
                                    <p class="text-red-400 font-medium text-sm">Ultimate Power</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€119.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">16 GB RAM</span>
                                    <span class="text-gray-300">8 vCPU</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">320 GB SSD</span>
                                    <span class="text-red-400 font-semibold">10Gbps</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-red-500 to-orange-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-red-600 hover:to-orange-700 transition-all duration-300">
                                    Order Now
                                </button>
                            </div>
                        </div>
                    </div>
                    </div>
                    
                    <!-- VPS View More Indicator positioned next to last card -->
                    <a href="vps.html" 
                    class="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-16 lg:block hidden">
                        <div class="text-center cursor-pointer">
                            <div class="text-gray-400 text-lg mb-1">•••</div>
                            <p class="text-xs text-gray-500">view more</p>
                        </div>
                    </a>
                </div>
            </div>
        </section>

        <!-- Dedicated Servers Section -->
        <section id="dedicated-streaming" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Dedicated Streaming Servers</h2>
                    <p class="text-gray-400 mt-2 max-w-2xl mx-auto">Ultimate performance for large-scale streaming operations with guaranteed bandwidth across 13 global locations.</p>
                </div>

                <div class="relative">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
                    <!-- Dedicated Server 1 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon E5-2690v4</h3>
                            <p class="text-gray-400 text-sm mb-4">High-performance dedicated server.</p>
                             <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€229</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>28 Core / 56 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>128 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>1/5/10Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>
                    
                    <!-- Dedicated Server 2 -->
                    <div class="pricing-card rounded-xl p-6">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon E5-2699v4</h3>
                            <p class="text-gray-400 text-sm mb-4">Premium dedicated streaming.</p>
                            <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€299</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>40 Core / 80 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>256 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>1/5/10Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>

                    <!-- Dedicated Server 3 -->
                    <div class="pricing-card rounded-xl p-6" style="background: linear-gradient(to right, rgba(30, 41, 59, 0.5) 0%, rgba(30, 41, 59, 0.5) 70%, rgba(30, 41, 59, 0) 100%);">
                        <div class="card-content">
                            <h3 class="text-xl font-semibold text-white">Dual Xeon Gold 6148</h3>
                            <p class="text-gray-400 text-sm mb-4">Ultimate streaming performance.</p>
                             <div class="my-4">
                                <span class="text-4xl font-extrabold text-white">€1799</span>
                                <span class="text-gray-400">/mo</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex items-center"><i data-lucide="cpu" class="w-4 h-4 mr-2 text-sky-400"></i>40 Core / 80 Threads</li>
                                <li class="flex items-center"><i data-lucide="memory-stick" class="w-4 h-4 mr-2 text-sky-400"></i>256 GB RAM</li>
                                <li class="flex items-center"><i data-lucide="hard-drive" class="w-4 h-4 mr-2 text-sky-400"></i>1 x 240GB SSD</li>
                                <li class="flex items-center"><i data-lucide="wifi" class="w-4 h-4 mr-2 text-sky-400"></i>25/50/100Gbps Guaranteed</li>
                                <li class="flex items-center"><i data-lucide="map-pin" class="w-4 h-4 mr-2 text-sky-400"></i>🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</li>
                                <li class="flex items-center"><i data-lucide="clock" class="w-4 h-4 mr-2 text-sky-400"></i>1 Hour Deployment</li>
                            </ul>
                        </div>
                        <a href="#" class="btn-primary w-full text-center mt-6 px-6 py-3 rounded-lg font-semibold">Configure</a>
                    </div>
                    </div>
                    
                    <!-- Dedicated View More Indicator positioned next to last card -->
                            <a href="dedicated.html" 
                   class="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-16 lg:block hidden">
                        <div class="text-center">
                            <div class="text-gray-400 text-lg mb-1">•••</div>
                            <p class="text-xs text-gray-500">view more</p>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Locations Section -->
        <section class="py-16 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Global Server Locations</h2>
                    <p class="text-gray-400 mt-2">Premium datacenters across Europe and US</p>
                </div>
                
                <!-- Slider Container -->
                <div class="relative overflow-hidden">
                    <div id="location-slider" class="flex transition-transform duration-500 ease-in-out">
                        <!-- Slide 1 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇷🇴</div>
                                    <h5 class="text-white text-sm font-semibold">Bucharest</h5>
                                    <p class="text-gray-400 text-xs">Voxility IRD</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Milan</h5>
                                    <p class="text-gray-400 text-xs">Irideos, Seeweb</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Rome</h5>
                                    <p class="text-gray-400 text-xs">Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇨🇿</div>
                                    <h5 class="text-white text-sm font-semibold">Prague</h5>
                                    <p class="text-gray-400 text-xs">CE Colo</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 2 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇪🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Madrid</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇫🇷</div>
                                    <h5 class="text-white text-sm font-semibold">Paris</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇫🇷</div>
                                    <h5 class="text-white text-sm font-semibold">Marseille</h5>
                                    <p class="text-gray-400 text-xs">Multiple DCs</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇬🇧</div>
                                    <h5 class="text-white text-sm font-semibold">London</h5>
                                    <p class="text-gray-400 text-xs">Telehouse, Equinix</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 3 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇮🇪</div>
                                    <h5 class="text-white text-sm font-semibold">Dublin</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇸🇪</div>
                                    <h5 class="text-white text-sm font-semibold">Stockholm</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇨🇭</div>
                                    <h5 class="text-white text-sm font-semibold">Zurich</h5>
                                    <p class="text-gray-400 text-xs">Equinix, Interxion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇵🇱</div>
                                    <h5 class="text-white text-sm font-semibold">Warsaw</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Slide 4 -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇧🇬</div>
                                    <h5 class="text-white text-sm font-semibold">Sofia</h5>
                                    <p class="text-gray-400 text-xs">Telepoint Centre</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Ashburn</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Miami</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Dallas</h5>
                                    <p class="text-gray-400 text-xs">Equinix</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Dots -->
                    <div class="flex justify-center mt-6 space-x-2">
                        <button class="slider-dot w-3 h-3 rounded-full bg-sky-400 opacity-100"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="slider-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Premium Features Included</h2>
                    <p class="text-gray-400 mt-2">Everything you need for a powerful hosting experience.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="wifi" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">10Gbps Network Speed</h3>
                        <p class="text-gray-400">Lightning-fast 10Gbps dedicated network ports for seamless streaming and content delivery.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="shield-check" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Free DDoS Protection</h3>
                        <p class="text-gray-400">Advanced DDoS protection included at no extra cost, keeping your streaming services online 24/7.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="map-pin" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Offshore Hosting</h3>
                        <p class="text-gray-400">Strategic offshore locations providing enhanced privacy and content distribution flexibility.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="play-circle" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Streaming Optimized</h3>
                        <p class="text-gray-400">Specially configured servers optimized for streaming, transcoding, and media delivery.</p>
                    </div>
                     <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="hard-drive" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">NVMe Storage</h3>
                        <p class="text-gray-400">Ultra-fast NVMe SSD storage ensuring rapid content loading and smooth streaming performance.</p>
                    </div>
                    <div class="feature-card p-6 rounded-lg">
                        <i data-lucide="arrow-right-left" class="w-10 h-10 mb-4 text-sky-400"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Unmetered Bandwidth</h3>
                        <p class="text-gray-400">No bandwidth limits or overage charges - stream as much content as you need.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Frequently Asked Questions</h2>
                    <p class="text-gray-400 mt-2">Your questions about our services, answered.</p>
                </div>
                <div class="space-y-4">
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What makes your streaming servers different?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Our streaming servers are specifically optimized for IPTV and media delivery with 10Gbps network speeds, offshore locations for privacy, and specialized configurations for streaming applications. We provide unmetered bandwidth and free DDoS protection as standard.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            Do you support streaming applications?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           Yes! Our servers are specifically configured for streaming with optimized network routing, transcoding capabilities, and content delivery features. We support popular streaming software and provide setup assistance.
                        </div>
                    </details>
                     <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           What offshore locations do you offer?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           We operate in privacy-friendly offshore jurisdictions that provide excellent connectivity and legal protection for content streaming. Our locations are strategically chosen for optimal performance and regulatory advantages.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           How fast can my server be deployed?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           VPS servers are deployed instantly upon payment confirmation. Dedicated streaming servers are typically provisioned within 24-48 hours with full configuration and optimization for your streaming needs.
                        </div>
                    </details>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        // Particle background animation
        const particleCanvas = document.getElementById('particle-canvas');
        if (particleCanvas) {
            const ctx = particleCanvas.getContext('2d');
            const heroSection = document.querySelector('.hero-gradient');
            let particlesArray;

            function setCanvasSize() {
                particleCanvas.width = heroSection.offsetWidth;
                particleCanvas.height = heroSection.offsetHeight;
            }

            class Particle {
                constructor(x, y, directionX, directionY, size, color) {
                    this.x = x;
                    this.y = y;
                    this.directionX = directionX;
                    this.directionY = directionY;
                    this.size = size;
                    this.color = color;
                }
                draw() {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                }
                update() {
                    if (this.x > particleCanvas.width || this.x < 0) {
                        this.directionX = -this.directionX;
                    }
                    if (this.y > particleCanvas.height || this.y < 0) {
                        this.directionY = -this.directionY;
                    }
                    this.x += this.directionX;
                    this.y += this.directionY;
                    this.draw();
                }
            }

            function init() {
                particlesArray = [];
                let numberOfParticles = (particleCanvas.height * particleCanvas.width) / 12000;
                for (let i = 0; i < numberOfParticles; i++) {
                    let size = (Math.random() * 1.5) + 1;
                    let x = (Math.random() * ((particleCanvas.width - size * 2) - (size * 2)) + size * 2);
                    let y = (Math.random() * ((particleCanvas.height - size * 2) - (size * 2)) + size * 2);
                    let directionX = (Math.random() * 0.4) - 0.2;
                    let directionY = (Math.random() * 0.4) - 0.2;
                    let color = 'rgba(56, 189, 248, 0.7)';
                    particlesArray.push(new Particle(x, y, directionX, directionY, size, color));
                }
            }

            function connect() {
                let opacityValue = 1;
                for (let a = 0; a < particlesArray.length; a++) {
                    for (let b = a; b < particlesArray.length; b++) {
                        let distance = ((particlesArray[a].x - particlesArray[b].x) * (particlesArray[a].x - particlesArray[b].x)) +
                            ((particlesArray[a].y - particlesArray[b].y) * (particlesArray[a].y - particlesArray[b].y));
                        
                        let maxDistance = 150;
                        if (distance < maxDistance * maxDistance) {
                            opacityValue = 1 - (distance / (maxDistance * maxDistance));
                            ctx.strokeStyle = `rgba(56, 189, 248, ${opacityValue * 0.8})`;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
                            ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
                            ctx.stroke();
                        }
                    }
                }
            }

            function animate() {
                requestAnimationFrame(animate);
                ctx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
                for (let i = 0; i < particlesArray.length; i++) {
                    particlesArray[i].update();
                }
                connect();
            }

            window.addEventListener('resize', () => {
                setCanvasSize();
                init();
            });

            setCanvasSize();
            init();
            animate();
        }
    </script>

    <script>
        lucide.createIcons();

        // FAQ accordion behavior
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(details => {
            details.addEventListener('toggle', event => {
                const icon = details.querySelector('summary i');
                if (details.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // Location slider functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('#location-slider > div');
        const dots = document.querySelectorAll('.slider-dot');
        const slider = document.getElementById('location-slider');

        function updateSlider() {
            slider.style.transform = `translateX(-${currentSlide * 100}%)`;
            dots.forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.remove('bg-gray-400', 'opacity-50');
                    dot.classList.add('bg-sky-400', 'opacity-100');
                } else {
                    dot.classList.remove('bg-sky-400', 'opacity-100');
                    dot.classList.add('bg-gray-400', 'opacity-50');
                }
            });
        }

        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlider();
            });
        });

        // Auto-advance slider
        setInterval(() => {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlider();
        }, 4000);

        // Animated Lines Background
        const canvas = document.getElementById('lines-canvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            let animationId;

            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            const lines = [];
            const numLines = 10;

            // Create lines with homepage theme colors (blues, purples, pinks)
            for (let i = 0; i < numLines; i++) {
                lines.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    length: Math.random() * 200 + 100,
                    angle: Math.random() * Math.PI * 2,
                    speed: Math.random() * 0.5 + 0.2,
                    opacity: Math.random() * 0.3 + 0.1,
                    color: i % 4 === 0 ? '#3b82f6' : i % 4 === 1 ? '#8b5cf6' : i % 4 === 2 ? '#ec4899' : '#06b6d4'
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                lines.forEach(line => {
                    // Update position
                    line.x += Math.cos(line.angle) * line.speed;
                    line.y += Math.sin(line.angle) * line.speed;

                    // Wrap around edges
                    if (line.x > canvas.width + line.length) line.x = -line.length;
                    if (line.x < -line.length) line.x = canvas.width + line.length;
                    if (line.y > canvas.height + line.length) line.y = -line.length;
                    if (line.y < -line.length) line.y = canvas.height + line.length;

                    // Draw line with gradient
                    const gradient = ctx.createLinearGradient(
                        line.x, line.y,
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    gradient.addColorStop(0, `${line.color}00`);
                    gradient.addColorStop(0.5, `${line.color}${Math.floor(line.opacity * 255).toString(16).padStart(2, '0')}`);
                    gradient.addColorStop(1, `${line.color}00`);

                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = 2;
                    ctx.lineCap = 'round';

                    ctx.beginPath();
                    ctx.moveTo(line.x, line.y);
                    ctx.lineTo(
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    ctx.stroke();
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();

            // Cleanup on page unload
            window.addEventListener('beforeunload', () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            });
        }

    </script>
</body>
</html>