<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPS Hosting Plans - X-ZoneServers | High-Performance KVM Virtual Servers</title>
    <meta name="description" content="X-ZoneServers KVM VPS hosting with 10Gbps bandwidth, SSD storage, unmetered traffic. Instant delivery starting from €9.50/month.">
    <meta name="keywords" content="KVM VPS, virtual private servers, VPS hosting, 10gbps VPS, offshore VPS, instant deployment">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>

</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <!-- Left Content -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                INSTANT DEPLOY
                            </div>
                            <div class="text-gray-400 text-sm">⚡ Ready in 60 Seconds</div>
                        </div>

                        <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                            Premium<br>
                            <span class="bg-gradient-to-r from-green-400 via-emerald-500 to-teal-600 bg-clip-text text-transparent">
                                KVM VPS
                            </span>
                        </h1>

                        <p class="text-xl text-gray-300 mb-8">
                            Experience unmatched performance with our KVM virtual private servers. Get dedicated resources, 10Gbps bandwidth, and enterprise-grade infrastructure with instant deployment.
                        </p>

                        <div class="flex flex-wrap gap-6 mb-12">
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                <span class="text-sm">Instant Deployment</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-emerald-400 rounded-full mr-3"></div>
                                <span class="text-sm">KVM Virtualization</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-teal-400 rounded-full mr-3"></div>
                                <span class="text-sm">Unmetered Traffic</span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#vps-plans" class="btn-primary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="server" class="w-5 h-5 mr-2"></i>
                                Browse VPS Plans
                            </a>
                            <a href="#features" class="btn-secondary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                                Learn More
                            </a>
                        </div>
                    </div>

                    <!-- Right Content - Floating Stats -->
                    <div class="relative hidden lg:block">
                        <!-- Floating Performance Cards -->
                        <div class="space-y-6">
                            <!-- Top Card -->
                            <div class="bg-gradient-to-r from-green-500/10 to-emerald-600/10 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">10Gbps</div>
                                        <div class="text-green-400 text-sm font-medium">Network Speed</div>
                                    </div>
                                    <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="zap" class="w-6 h-6 text-green-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Middle Card -->
                            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-600/10 backdrop-blur-sm border border-emerald-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300 ml-8">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">99.9%</div>
                                        <div class="text-emerald-400 text-sm font-medium">Uptime SLA</div>
                                    </div>
                                    <div class="w-12 h-12 bg-emerald-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="shield-check" class="w-6 h-6 text-emerald-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Card -->
                            <div class="bg-gradient-to-r from-teal-500/10 to-cyan-600/10 backdrop-blur-sm border border-teal-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">60s</div>
                                        <div class="text-teal-400 text-sm font-medium">Deploy Time</div>
                                    </div>
                                    <div class="w-12 h-12 bg-teal-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="clock" class="w-6 h-6 text-teal-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-green-400/20 to-emerald-600/20 rounded-full blur-xl"></div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-teal-400/20 to-cyan-600/20 rounded-full blur-xl"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">10Gbps</div>
                        <div class="text-green-400 font-medium text-sm">Max Bandwidth</div>
                        <div class="text-gray-400 text-xs">Shared network</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">16</div>
                        <div class="text-emerald-400 font-medium text-sm">Max vCPU</div>
                        <div class="text-gray-400 text-xs">Dedicated cores</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">32GB</div>
                        <div class="text-teal-400 font-medium text-sm">Max RAM</div>
                        <div class="text-gray-400 text-xs">DDR4 memory</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">60s</div>
                        <div class="text-cyan-400 font-medium text-sm">Deploy Time</div>
                        <div class="text-gray-400 text-xs">Instant setup</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VPS Configurator Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Choose Your Perfect VPS</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Select the plan that fits your needs - all with instant deployment, unmetered bandwidth, and enterprise-grade infrastructure.
                    </p>
                </div>

                <div class="max-w-6xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                        <div class="group">
                            <div class="bg-gradient-to-br from-green-500/10 to-emerald-600/10 p-8 rounded-xl border border-green-500/20 h-full transition-all duration-300 hover:border-green-500/50 hover:shadow-xl hover:shadow-green-500/10">
                                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                                    <i data-lucide="server" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-white mb-4">KVM Virtualization</h3>
                                <p class="text-gray-400">Full hardware virtualization with guaranteed dedicated resources and complete isolation from other users.</p>
                            </div>
                        </div>
                        <div class="group">
                            <div class="bg-gradient-to-br from-teal-500/10 to-cyan-600/10 p-8 rounded-xl border border-teal-500/20 h-full transition-all duration-300 hover:border-teal-500/50 hover:shadow-xl hover:shadow-teal-500/10">
                                <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                                    <i data-lucide="hard-drive" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-white mb-4">SSD Storage</h3>
                                <p class="text-gray-400">High-performance SSD storage arrays for lightning-fast data access and superior I/O performance.</p>
                            </div>
                        </div>
                        <div class="group">
                            <div class="bg-gradient-to-br from-cyan-500/10 to-blue-600/10 p-8 rounded-xl border border-cyan-500/20 h-full transition-all duration-300 hover:border-cyan-500/50 hover:shadow-xl hover:shadow-cyan-500/10">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                                    <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-white mb-4">Instant Deployment</h3>
                                <p class="text-gray-400">Your VPS is ready in under 60 seconds with automated setup and immediate access to your server.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VPS Pricing Section -->
        <section id="vps-plans" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">VPS Hosting Plans</h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">All plans include unmetered bandwidth, 10Gbps network, and instant deployment</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- VPS Plan 1 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-blue-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS I KVM</h4>
                                    <p class="text-blue-400 font-medium text-sm">Entry Level</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€9.50</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">2 GB RAM</span>
                                    <span class="text-gray-300">1 vCPU Core</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">20 GB SSD Storage</span>
                                    <span class="text-blue-400 font-semibold">1-2Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-blue-500/50 text-blue-400 rounded-lg hover:bg-blue-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 2 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-emerald-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS II KVM</h4>
                                    <p class="text-emerald-400 font-medium text-sm">Enhanced Performance</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€17.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">3 GB RAM</span>
                                    <span class="text-gray-300">1 vCPU Core</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">40 GB SSD Storage</span>
                                    <span class="text-emerald-400 font-semibold">2-3Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-emerald-600 hover:to-teal-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-emerald-500/50 text-emerald-400 rounded-lg hover:bg-emerald-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 3 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-purple-500/50 hover:bg-slate-800/50">
                            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-bold">
                                    POPULAR
                                </div>
                            </div>

                            <div class="flex items-center justify-between mb-4 mt-2">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS III KVM</h4>
                                    <p class="text-purple-400 font-medium text-sm">Dual-Core Power</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€26.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">4 GB RAM</span>
                                    <span class="text-gray-300">2 vCPU Cores</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">80 GB SSD Storage</span>
                                    <span class="text-purple-400 font-semibold">3-4Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-purple-500/50 text-purple-400 rounded-lg hover:bg-purple-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- VPS Plan 4 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-yellow-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS IV KVM</h4>
                                    <p class="text-yellow-400 font-medium text-sm">Quad-Core Performance</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€64.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">8 GB RAM</span>
                                    <span class="text-gray-300">4 vCPU Cores</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">160 GB SSD Storage</span>
                                    <span class="text-yellow-400 font-semibold">4-5Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-yellow-500 to-orange-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-yellow-600 hover:to-orange-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-yellow-500/50 text-yellow-400 rounded-lg hover:bg-yellow-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 5 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-cyan-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS V KVM</h4>
                                    <p class="text-cyan-400 font-medium text-sm">High Performance</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€119.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">16 GB RAM</span>
                                    <span class="text-gray-300">8 vCPU Cores</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">320 GB SSD Storage</span>
                                    <span class="text-cyan-400 font-semibold">5-6Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-cyan-600 hover:to-blue-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-cyan-500/50 text-cyan-400 rounded-lg hover:bg-cyan-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- VPS Plan 6 -->
                    <div class="group relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                        <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h4 class="text-xl font-bold text-white">VPS VI KVM</h4>
                                    <p class="text-red-400 font-medium text-sm">Ultimate Performance</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">€269.99</div>
                                    <div class="text-gray-400 text-xs">/month</div>
                                </div>
                            </div>

                            <div class="space-y-3 mb-6 text-sm">
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">32 GB RAM</span>
                                    <span class="text-gray-300">16 vCPU Cores</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">640 GB SSD Storage</span>
                                    <span class="text-red-400 font-semibold">6-7Gbps Est.</span>
                                </div>
                                <div class="flex items-center justify-between py-1">
                                    <span class="text-gray-300">Unmetered Traffic</span>
                                    <span class="text-green-400">✓ Included</span>
                                </div>
                            </div>

                            <div class="flex gap-3">
                                <button class="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-red-600 hover:to-pink-700 transition-all duration-300">
                                    Order Now
                                </button>
                                <button class="px-4 py-2 border border-red-500/50 text-red-400 rounded-lg hover:bg-red-500/10 transition-colors">
                                    Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comparison Table -->
                <div class="mt-16 bg-gradient-to-r from-slate-900/50 to-slate-800/50 rounded-3xl p-8 border border-slate-700/50">
                    <h3 class="text-2xl font-bold text-white mb-6 text-center">Complete VPS Comparison</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-slate-700">
                                    <th class="text-left py-3 px-4 text-gray-300 font-medium">Features</th>
                                    <th class="text-center py-3 px-4 text-blue-400 font-medium">VPS I</th>
                                    <th class="text-center py-3 px-4 text-emerald-400 font-medium">VPS II</th>
                                    <th class="text-center py-3 px-4 text-purple-400 font-medium">VPS III</th>
                                    <th class="text-center py-3 px-4 text-yellow-400 font-medium">VPS IV</th>
                                    <th class="text-center py-3 px-4 text-cyan-400 font-medium">VPS V</th>
                                    <th class="text-center py-3 px-4 text-red-400 font-medium">VPS VI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-slate-800/50">
                                    <td class="py-3 px-4 text-gray-300">RAM Memory</td>
                                    <td class="py-3 px-4 text-center text-white">2 GB</td>
                                    <td class="py-3 px-4 text-center text-white">3 GB</td>
                                    <td class="py-3 px-4 text-center text-white">4 GB</td>
                                    <td class="py-3 px-4 text-center text-white">8 GB</td>
                                    <td class="py-3 px-4 text-center text-white">16 GB</td>
                                    <td class="py-3 px-4 text-center text-white">32 GB</td>
                                </tr>
                                <tr class="border-b border-slate-800/50">
                                    <td class="py-3 px-4 text-gray-300">vCPU Cores</td>
                                    <td class="py-3 px-4 text-center text-white">1</td>
                                    <td class="py-3 px-4 text-center text-white">1</td>
                                    <td class="py-3 px-4 text-center text-white">2</td>
                                    <td class="py-3 px-4 text-center text-white">4</td>
                                    <td class="py-3 px-4 text-center text-white">8</td>
                                    <td class="py-3 px-4 text-center text-white">16</td>
                                </tr>
                                <tr class="border-b border-slate-800/50">
                                    <td class="py-3 px-4 text-gray-300">SSD Storage</td>
                                    <td class="py-3 px-4 text-center text-white">20 GB</td>
                                    <td class="py-3 px-4 text-center text-white">40 GB</td>
                                    <td class="py-3 px-4 text-center text-white">80 GB</td>
                                    <td class="py-3 px-4 text-center text-white">160 GB</td>
                                    <td class="py-3 px-4 text-center text-white">320 GB</td>
                                    <td class="py-3 px-4 text-center text-white">640 GB</td>
                                </tr>
                                <tr class="border-b border-slate-800/50">
                                    <td class="py-3 px-4 text-gray-300">Est. Min Speed</td>
                                    <td class="py-3 px-4 text-center text-blue-400">1-2 Gbps</td>
                                    <td class="py-3 px-4 text-center text-emerald-400">2-3 Gbps</td>
                                    <td class="py-3 px-4 text-center text-purple-400">3-4 Gbps</td>
                                    <td class="py-3 px-4 text-center text-yellow-400">4-5 Gbps</td>
                                    <td class="py-3 px-4 text-center text-cyan-400">5-6 Gbps</td>
                                    <td class="py-3 px-4 text-center text-red-400">6-7 Gbps</td>
                                </tr>
                                <tr>
                                    <td class="py-3 px-4 text-gray-300">Monthly Price</td>
                                    <td class="py-3 px-4 text-center text-blue-400 font-bold">€9.50</td>
                                    <td class="py-3 px-4 text-center text-emerald-400 font-bold">€17.99</td>
                                    <td class="py-3 px-4 text-center text-purple-400 font-bold">€26.99</td>
                                    <td class="py-3 px-4 text-center text-yellow-400 font-bold">€64.99</td>
                                    <td class="py-3 px-4 text-center text-cyan-400 font-bold">€119.99</td>
                                    <td class="py-3 px-4 text-center text-red-400 font-bold">€269.99</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-xs text-gray-500 mt-4 text-center">
                        All plans include: 10Gbps bandwidth, unmetered traffic, instant deployment, KVM virtualization, root access
                    </p>
                </div>
            </div>
        </section>

        <!-- Technical Specifications Section -->
        <section class="py-20 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Technical Specifications</h2>
                    <p class="text-gray-400 mt-2">Enterprise-grade infrastructure powering your VPS</p>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <div>
                        <div class="bg-slate-800/30 rounded-xl p-8 border border-slate-700/50">
                            <h3 class="text-xl font-bold text-white mb-6">Hardware & Infrastructure</h3>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Enterprise SSDs</div>
                                        <div class="text-gray-400 text-sm">High-performance NVMe and SSD storage arrays</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Premium CPUs</div>
                                        <div class="text-gray-400 text-sm">Latest Intel Xeon and AMD EPYC processors</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">ECC Memory</div>
                                        <div class="text-gray-400 text-sm">Error-correcting code memory for reliability</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Redundant Power</div>
                                        <div class="text-gray-400 text-sm">Dual power feeds with UPS backup</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="bg-slate-800/30 rounded-xl p-8 border border-slate-700/50">
                            <h3 class="text-xl font-bold text-white mb-6">Network & Connectivity</h3>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">10Gbps Network Ports</div>
                                        <div class="text-gray-400 text-sm">Dedicated 10Gbps connectivity per server</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Premium Tier-1 Networks</div>
                                        <div class="text-gray-400 text-sm">Direct peering with major ISPs worldwide</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Low Latency Routing</div>
                                        <div class="text-gray-400 text-sm">Optimized routes for global connectivity</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sky-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">DDoS Protection</div>
                                        <div class="text-gray-400 text-sm">Advanced DDoS mitigation included</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VPS Use Cases Section -->
        <section id="features" class="py-20 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Perfect for Every Use Case</h2>
                    <p class="text-gray-400 mt-2">Discover what you can build with our high-performance VPS hosting</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-sky-500/50 hover:shadow-xl hover:shadow-sky-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Web Applications</h3>
                            <p class="text-gray-400 mb-4">Host high-traffic websites, web applications, and APIs with guaranteed resources and blazing-fast performance.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>WordPress & CMS hosting</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>E-commerce platforms</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Custom web applications</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-sky-500/50 hover:shadow-xl hover:shadow-sky-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="database" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Database Servers</h3>
                            <p class="text-gray-400 mb-4">Run MySQL, PostgreSQL, MongoDB, and other database systems with optimized performance and reliability.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>High IOPS SSD storage</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Dedicated CPU cores</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Memory optimization</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-sky-500/50 hover:shadow-xl hover:shadow-sky-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="code" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Development & Testing</h3>
                            <p class="text-gray-400 mb-4">Create isolated development environments, run CI/CD pipelines, and test applications at scale.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Multiple OS support</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Root access & flexibility</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-sky-400"></i>Snapshot capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Features Row -->
                <div class="bg-gradient-to-r from-slate-800/30 to-slate-900/30 rounded-2xl p-8 border border-slate-700/50">
                    <h3 class="text-2xl font-bold text-white mb-8 text-center">Included with Every VPS</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-sky-500/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="shield-check" class="w-6 h-6 text-sky-400"></i>
                            </div>
                            <div class="text-white font-semibold">DDoS Protection</div>
                            <div class="text-gray-400 text-sm mt-1">Advanced filtering</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-sky-500/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="monitor" class="w-6 h-6 text-sky-400"></i>
                            </div>
                            <div class="text-white font-semibold">Control Panel</div>
                            <div class="text-gray-400 text-sm mt-1">Easy management</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-sky-500/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="headphones" class="w-6 h-6 text-sky-400"></i>
                            </div>
                            <div class="text-white font-semibold">24/7 Support</div>
                            <div class="text-gray-400 text-sm mt-1">Expert assistance</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-sky-500/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="refresh-cw" class="w-6 h-6 text-sky-400"></i>
                            </div>
                            <div class="text-white font-semibold">99.9% SLA</div>
                            <div class="text-gray-400 text-sm mt-1">Guaranteed uptime</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">VPS Hosting FAQ</h2>
                    <p class="text-gray-400 mt-2">Common questions about our VPS hosting services.</p>
                </div>
                <div class="space-y-4">
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What is KVM virtualization?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            KVM (Kernel-based Virtual Machine) is a full virtualization solution that provides dedicated resources and complete isolation between virtual machines. Unlike shared hosting, your VPS resources are guaranteed and not shared with other users.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            How quickly will my VPS be deployed?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           Your VPS is deployed instantly upon payment confirmation. You'll receive login credentials and can start using your server within minutes of ordering.
                        </div>
                    </details>
                     <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           Can I upgrade my VPS plan later?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           Yes, you can upgrade your VPS to a higher plan at any time. Upgrades are processed quickly with minimal downtime to ensure your services continue running smoothly.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                           What operating systems are supported?
                           <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           We support various Linux distributions including Ubuntu, CentOS, Debian, and others. Custom OS installations can also be arranged upon request.
                        </div>
                    </details>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>


    <script>
        lucide.createIcons();

        // FAQ accordion behavior
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(details => {
            details.addEventListener('toggle', event => {
                const icon = details.querySelector('summary i');
                if (details.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // Animated Lines Background
        const canvas = document.getElementById('lines-canvas');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            let animationId;

            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            const lines = [];
            const numLines = 8;

            // Create lines with VPS-themed colors (greens, teals, cyans)
            for (let i = 0; i < numLines; i++) {
                lines.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    length: Math.random() * 200 + 100,
                    angle: Math.random() * Math.PI * 2,
                    speed: Math.random() * 0.5 + 0.2,
                    opacity: Math.random() * 0.3 + 0.1,
                    color: i % 4 === 0 ? '#10b981' : i % 4 === 1 ? '#14b8a6' : i % 4 === 2 ? '#06b6d4' : '#22d3ee'
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                lines.forEach(line => {
                    // Update position
                    line.x += Math.cos(line.angle) * line.speed;
                    line.y += Math.sin(line.angle) * line.speed;

                    // Wrap around edges
                    if (line.x > canvas.width + line.length) line.x = -line.length;
                    if (line.x < -line.length) line.x = canvas.width + line.length;
                    if (line.y > canvas.height + line.length) line.y = -line.length;
                    if (line.y < -line.length) line.y = canvas.height + line.length;

                    // Draw line with gradient
                    const gradient = ctx.createLinearGradient(
                        line.x, line.y,
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    gradient.addColorStop(0, `${line.color}00`);
                    gradient.addColorStop(0.5, `${line.color}${Math.floor(line.opacity * 255).toString(16).padStart(2, '0')}`);
                    gradient.addColorStop(1, `${line.color}00`);

                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = 2;
                    ctx.lineCap = 'round';

                    ctx.beginPath();
                    ctx.moveTo(line.x, line.y);
                    ctx.lineTo(
                        line.x + Math.cos(line.angle) * line.length,
                        line.y + Math.sin(line.angle) * line.length
                    );
                    ctx.stroke();
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();

            // Cleanup on page unload
            window.addEventListener('beforeunload', () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            });
        }
    </script>
</body>
</html>